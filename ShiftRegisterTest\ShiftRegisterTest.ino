/*
 * Shift Register Bit Mapping Test for ESP8266 3-Relay Device
 * 
 * This test systematically turns on each bit position one at a time
 * to help determine the correct hardware mapping.
 * 
 * Data structure: x1x2x3x4x5x6x7x8 x9x10x11x12x13x14x15x16
 * Transmission order: x9x10x11x12x13x14x15x16x1x2x3x4x5x6x7x8
 * 
 * First byte (x1-x8) -> Second shift register
 * Second byte (x9-x16) -> First shift register
 */

// Pin definitions for 3-relay variant
const uint8_t SR_DATA_PIN = 13;  // Serial data
const uint8_t SR_CLOCK_PIN = 16; // Shift register clock
const uint8_t SR_LATCH_PIN = 14; // Storage register clock

// Test parameters
const unsigned long TEST_DELAY = 5000; // 3 seconds per bit test
const unsigned long BLINK_DELAY = 00; // 0.5 seconds for blinking

void setup() {
  Serial.begin(115200);
  Serial.println("\n=== Shift Register Bit Mapping Test ===");
  Serial.println("3-Relay ESP8266 Device");
  Serial.println("Pin Configuration:");
  Serial.print("Data Pin: "); Serial.println(SR_DATA_PIN);
  Serial.print("Clock Pin: "); Serial.println(SR_CLOCK_PIN);
  Serial.print("Latch Pin: "); Serial.println(SR_LATCH_PIN);
  
  // Initialize shift register pins
  pinMode(SR_DATA_PIN, OUTPUT);
  pinMode(SR_CLOCK_PIN, OUTPUT);
  pinMode(SR_LATCH_PIN, OUTPUT);
  
  // Clear all outputs initially
  updateShiftRegisters(0x0000);
  
  Serial.println("\nStarting bit mapping test...");
  Serial.println("Each bit will be tested for 3 seconds with blinking");
  Serial.println("Watch your hardware to see which component responds");
  Serial.println("Data format: x9x10x11x12x13x14x15x16x1x2x3x4x5x6x7x8");
  Serial.println("===========================================\n");
  
  delay(0); // Give time to read the setup info
}

void loop() {
  // Test each bit position (0-15)
  for (uint8_t bitPos = 0; bitPos < 16; bitPos++) {
    testBitPosition(bitPos);
    delay(1000); // Brief pause between tests
  }
  
  Serial.println("=== Test cycle complete. Repeating... ===\n");
  delay(500);
}

void testBitPosition(uint8_t bitPos) {
  uint16_t testValue = (1 << bitPos);
  
  Serial.print("Testing Bit ");
  Serial.print(bitPos);
  Serial.print(" (");
  
  // Show which register and position
  if (bitPos < 8) {
    Serial.print("x");
    Serial.print(bitPos + 1);
    Serial.print(" - Second Shift Register");
  } else {
    Serial.print("x");
    Serial.print(bitPos + 1);
    Serial.print(" - First Shift Register");
  }
  
  Serial.print(") - Value: 0x");
  Serial.print(testValue, HEX);
  Serial.print(" - Binary: ");
  printBinary16(testValue);
  Serial.println();
  
  // Blink the bit 3 times to make it obvious
  for (int i = 0; i < 6; i++) {
    if (i % 2 == 0) {
      updateShiftRegisters(testValue); // Turn on
    } else {
      updateShiftRegisters(0x0000);    // Turn off
    }
    delay(BLINK_DELAY);
  }
  
  // Keep it on for observation
  updateShiftRegisters(testValue);
  delay(TEST_DELAY - (6 * BLINK_DELAY));
  
  // Turn off
  updateShiftRegisters(0x0000);
  
  Serial.println("  ^ Note what component responded above ^");
  Serial.println();
}

void updateShiftRegisters(uint16_t outputState) {
  digitalWrite(SR_LATCH_PIN, LOW);
  
  // Send second byte first (bits 9-16, goes to first 74HC595)
  shiftOut(SR_DATA_PIN, SR_CLOCK_PIN, MSBFIRST, (outputState >> 8) & 0xFF);
  
  // Send first byte second (bits 1-8, goes to second 74HC595)
  shiftOut(SR_DATA_PIN, SR_CLOCK_PIN, MSBFIRST, outputState & 0xFF);
  
  digitalWrite(SR_LATCH_PIN, HIGH);
}

void printBinary16(uint16_t value) {
  for (int i = 15; i >= 0; i--) {
    Serial.print((value >> i) & 1);
    if (i == 8) Serial.print(" "); // Space between bytes for readability
  }
}
