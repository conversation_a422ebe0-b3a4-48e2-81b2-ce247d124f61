#ifndef SHIFT_REGISTER_MANAGER_H
#define SHIFT_REGISTER_MANAGER_H

#include <Arduino.h>

class ShiftRegisterManager
{
private:
    uint8_t _dataPin;
    uint8_t _clockPin;
    uint8_t _latchPin;
    uint16_t _outputState;

    // Hardware-specific bit mapping for ESP8266 dual shift register setup (3-Relay variant)
    // Data structure: x1x2x3x4x5x6x7x8 x9x10x11x12x13x14x15x16
    // First 74HC595 (x1-x8):       Second 74HC595 (x9-x16):
    // x1 -> none                   x9  -> none
    // x2 -> g2                     x10 -> g3
    // x3 -> b2                     x11 -> b3
    // x4 -> r2                     x12 -> r3
    // x5 -> R3 (Relay 3)           x13 -> g1
    // x6 -> R2 (Relay 2)           x14 -> b1
    // x7 -> R1 (Relay 1)           x15 -> r1
    // x8 -> none                   x16 -> none

    // Bit position arrays for hardware mapping (3-Relay variant)
    const uint8_t RELAY_BITS[3] = {6, 5, 4};           // R1, R2, R3 bit positions (0-indexed)
    const uint8_t RED_BITS[3] = {14, 3, 11};           // r1, r2, r3 bit positions
    const uint8_t GREEN_BITS[3] = {12, 1, 9};          // g1, g2, g3 bit positions  
    const uint8_t BLUE_BITS[3] = {13, 2, 10};          // b1, b2, b3 bit positions

    // Update the physical shift registers with current state
    void updateShiftRegisters()
    {
        digitalWrite(_latchPin, LOW);
        
        // Send bits 9-16 first (goes to second 74HC595)
        shiftOut(_dataPin, _clockPin, MSBFIRST, (_outputState >> 8) & 0xFF);
        
        // Send bits 1-8 second (stays in first 74HC595)  
        shiftOut(_dataPin, _clockPin, MSBFIRST, _outputState & 0xFF);
        
        digitalWrite(_latchPin, HIGH);
    }

public:
    ShiftRegisterManager(uint8_t dataPin, uint8_t clockPin, uint8_t latchPin)
        : _dataPin(dataPin), _clockPin(clockPin), _latchPin(latchPin), _outputState(0)
    {
    }

    // Initialize the shift register pins
    void begin()
    {
        pinMode(_dataPin, OUTPUT);
        pinMode(_clockPin, OUTPUT);
        pinMode(_latchPin, OUTPUT);
        
        // Initialize all outputs to LOW
        _outputState = 0;
        updateShiftRegisters();
        
        Serial.println("3-Relay Shift Register Manager initialized");
        Serial.print("Data Pin: ");
        Serial.println(_dataPin);
        Serial.print("Clock Pin: ");
        Serial.println(_clockPin);
        Serial.print("Latch Pin: ");
        Serial.println(_latchPin);
    }

    // Set relay state (relayIndex: 0-2 for relays 1-3)
    void setRelay(uint8_t relayIndex, bool state)
    {
        if (relayIndex >= 3) return;
        
        uint8_t bitPos = RELAY_BITS[relayIndex];
        
        if (state) {
            _outputState |= (1 << bitPos);
        } else {
            _outputState &= ~(1 << bitPos);
        }
        
        updateShiftRegisters();
        
        Serial.print("3-Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to ");
        Serial.println(state ? "ON" : "OFF");
    }

    // Get relay state
    bool getRelay(uint8_t relayIndex)
    {
        if (relayIndex >= 3) return false;
        
        uint8_t bitPos = RELAY_BITS[relayIndex];
        return (_outputState & (1 << bitPos)) != 0;
    }

    // Set RGB color for a specific relay button (relayIndex: 0-2 for relays 1-3)
    // Note: RGB values are digital (0=OFF, >0=ON) since 74HC595 provides digital outputs only
    void setRGB(uint8_t relayIndex, uint8_t r, uint8_t g, uint8_t b)
    {
        if (relayIndex >= 3) return;
        
        // Set Red (digital: 0=OFF, >0=ON)
        if (r > 0) {
            _outputState |= (1 << RED_BITS[relayIndex]);
        } else {
            _outputState &= ~(1 << RED_BITS[relayIndex]);
        }
        
        // Set Green (digital: 0=OFF, >0=ON)
        if (g > 0) {
            _outputState |= (1 << GREEN_BITS[relayIndex]);
        } else {
            _outputState &= ~(1 << GREEN_BITS[relayIndex]);
        }
        
        // Set Blue (digital: 0=OFF, >0=ON)
        if (b > 0) {
            _outputState |= (1 << BLUE_BITS[relayIndex]);
        } else {
            _outputState &= ~(1 << BLUE_BITS[relayIndex]);
        }
        
        updateShiftRegisters();
        
        Serial.print("3-Relay RGB for Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to R:");
        Serial.print(r > 0 ? "ON" : "OFF");
        Serial.print(" G:");
        Serial.print(g > 0 ? "ON" : "OFF");
        Serial.print(" B:");
        Serial.println(b > 0 ? "ON" : "OFF");
    }

    // Get RGB color for a specific relay button
    void getRGB(uint8_t relayIndex, uint8_t &r, uint8_t &g, uint8_t &b)
    {
        if (relayIndex >= 3)
        {
            r = g = b = 0;
            return;
        }
        
        r = (_outputState & (1 << RED_BITS[relayIndex])) ? 255 : 0;
        g = (_outputState & (1 << GREEN_BITS[relayIndex])) ? 255 : 0;
        b = (_outputState & (1 << BLUE_BITS[relayIndex])) ? 255 : 0;
    }

    // Set individual output bit (for advanced control)
    void setOutput(uint8_t bitIndex, bool state)
    {
        if (bitIndex >= 16) return;
        
        if (state) {
            _outputState |= (1 << bitIndex);
        } else {
            _outputState &= ~(1 << bitIndex);
        }
        
        updateShiftRegisters();
    }

    // Get individual output bit state
    bool getOutput(uint8_t bitIndex)
    {
        if (bitIndex >= 16) return false;
        return (_outputState & (1 << bitIndex)) != 0;
    }

    // Set all outputs at once
    void setAllOutputs(uint16_t state)
    {
        _outputState = state;
        updateShiftRegisters();
    }

    // Get current output state
    uint16_t getAllOutputs()
    {
        return _outputState;
    }

    // Clear all outputs
    void clearAll()
    {
        _outputState = 0;
        updateShiftRegisters();
        Serial.println("All 3-Relay shift register outputs cleared");
    }
};

#endif // SHIFT_REGISTER_MANAGER_H
